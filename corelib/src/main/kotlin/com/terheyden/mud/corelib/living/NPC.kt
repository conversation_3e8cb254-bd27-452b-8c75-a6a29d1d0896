package com.terheyden.mud.corelib.living

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.TheVoid

/**
 * Represents a Non-Player Character (NPC) in the game world.
 */
abstract class NPC(
    id: String,
    name: String,
    description: String,
    currentRoom: Room = TheVoid.Companion.Instance,
    inventory: Collection<Item> = emptyList(),
    maxInventoryWeight: Int = 50,
    maxHealthPoints: Int = 100,
    currentHealthPoints: Int = maxHealthPoints,
    experiencePoints: Int = 0,
    level: Int = 1,
    baseAttackPower: Int = 10,
    baseDefense: Int = 5,
    val npcType: NPCType = NPCType.NEUTRAL,
    val experienceReward: Int = level * 10,
    val lootTable: List<Item> = emptyList(),
) : Living(
    id = id,
    name = name,
    description = description,
    currentRoom = currentRoom,
    inventory = inventory,
    maxInventoryWeight = maxInventoryWeight,
    maxHealthPoints = maxHealthPoints,
    currentHealthPoints = currentHealthPoints,
    experiencePoints = experiencePoints,
    level = level,
    baseAttackPower = baseAttackPower,
    baseDefense = baseDefense,
) {

    /**
     * Handle dialogue when a player talks to this NPC.
     */
    abstract fun handleDialogue(player: Player, message: String): String

    /**
     * Get the default greeting when a player first encounters this NPC.
     */
    abstract fun getGreeting(player: Player): String

    /**
     * Check if this NPC is hostile to the player.
     */
    fun isHostileTo(player: Player): Boolean {
        return npcType == NPCType.AGGRESSIVE
    }

    /**
     * Check if this NPC is friendly to the player.
     */
    fun isFriendlyTo(player: Player): Boolean {
        return npcType == NPCType.FRIENDLY
    }

    /**
     * Get the items this NPC drops when defeated.
     */
    fun getDroppedLoot(): List<Item> {
        // For now, return all loot. Later could add randomization.
        return lootTable.toList()
    }

    /**
     * Handle what happens when this NPC dies.
     */
    fun onDeath(killer: Player): String {
        return buildString {
            appendLine("${name} has been defeated!")

            val loot = getDroppedLoot()
            if (loot.isNotEmpty()) {
                appendLine("${name} drops:")
                loot.forEach { item ->
                    getCurrentRoom().addItem(item)
                    appendLine("  - ${item.name}")
                }
            }

            if (experienceReward > 0) {
                val leveledUp = killer.gainExperience(experienceReward)
                appendLine("You gain $experienceReward experience points!")
                if (leveledUp) {
                    appendLine("Congratulations! You have reached level ${killer.level}!")
                }
            }
        }
    }

    /**
     * Get a combat action for this NPC during combat.
     * Override in specific NPC classes for custom behavior.
     */
    open fun getCombatAction(target: Living): String {
        return "attack" // Default action is to attack
    }

    /**
     * Handle NPC behavior when a player enters the room.
     */
    open fun onPlayerEnter(player: Player): String? {
        return when (npcType) {
            NPCType.AGGRESSIVE -> {
                "The ${name} notices you and growls menacingly!"
            }

            NPCType.FRIENDLY -> {
                getGreeting(player)
            }

            NPCType.NEUTRAL -> {
                "The ${name} notices your presence but seems indifferent."
            }
        }
    }

    /**
     * Get a description that includes the NPC's current state.
     */
    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The ${name} appears to be in ${getHealthDescription()}.")

            when (npcType) {
                NPCType.FRIENDLY -> appendLine("They seem friendly and approachable.")
                NPCType.NEUTRAL -> appendLine("They seem indifferent to your presence.")
                NPCType.AGGRESSIVE -> appendLine("They look hostile and dangerous!")
            }

            if (equippedWeapon != null) {
                appendLine("They are wielding ${equippedWeapon!!.name}.")
            }
        }
    }

    // === AUTONOMOUS BEHAVIOR ===

    /**
     * NPC-specific tick behavior for autonomous actions.
     */
    override fun onTickBehavior(tickCount: Long) {
        // Perform autonomous behavior based on NPC type
        when (npcType) {
            NPCType.AGGRESSIVE -> performAggressiveBehavior(tickCount)
            NPCType.NEUTRAL -> performNeutralBehavior(tickCount)
            NPCType.FRIENDLY -> performFriendlyBehavior(tickCount)
        }
    }

    /**
     * Aggressive NPCs look for targets and may attack.
     */
    protected open fun performAggressiveBehavior(tickCount: Long) {
        // Every 20 ticks, check for players in the room
        if (tickCount % 20 == 0L) {
            // TODO: Implement aggressive behavior when multi-player support is added
            // For now, just log that the NPC is being aggressive
        }
    }

    /**
     * Neutral NPCs may wander or perform idle actions.
     */
    protected open fun performNeutralBehavior(tickCount: Long) {
        // Every 50 ticks, neutral NPCs might do something
        if (tickCount % 50 == 0L) {
            // TODO: Implement wandering behavior
            // Could move to adjacent rooms, perform idle actions, etc.
        }
    }

    /**
     * Friendly NPCs may greet players or perform helpful actions.
     */
    protected open fun performFriendlyBehavior(tickCount: Long) {
        // Every 30 ticks, friendly NPCs might do something helpful
        if (tickCount % 30 == 0L) {
            // TODO: Implement friendly behavior
            // Could offer help, give hints, heal players, etc.
        }
    }

    /**
     * NPCs tick less frequently than players for performance.
     */
    override fun getTickInterval(): Int = 10

    // === AUTONOMOUS SPEECH ===

    /**
     * Make this NPC say something to the room.
     * This will be visible to any players in the same room.
     */
    protected fun sayToRoom(message: String) {
        // For now, we'll store the message for the next time a player looks or moves
        // In a full multi-player system, this would broadcast to all players in the room
        getCurrentRoom().addRoomMessage("${name} says: \"$message\"")
    }

    /**
     * Make this NPC perform an action that's visible to the room.
     */
    protected fun performAction(action: String) {
        getCurrentRoom().addRoomMessage("${name} $action")
    }
}

/**
 * Types of NPCs based on their behavior toward players.
 */
enum class NPCType(val displayName: String) {
    FRIENDLY("Friendly"),
    NEUTRAL("Neutral"),
    AGGRESSIVE("Aggressive")
}
