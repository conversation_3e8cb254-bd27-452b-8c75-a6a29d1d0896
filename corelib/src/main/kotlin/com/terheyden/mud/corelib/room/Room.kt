package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Container
import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.Initializable
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.living.Living
import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.tick.Tickable
import kotlin.reflect.KClass

/**
 * Base class for all rooms in the game world.
 * Extends Container so rooms can hold items.
 */
abstract class Room(
    id: String,
    name: String,
    description: String,
    features: List<RoomFeature> = emptyList(),
    val exits: MutableMap<Direction, KClass<out Room>>,
    doors: List<Door> = emptyList(),
    items: Collection<Item> = emptyList(),
) : Container(
    id = id,
    name = name,
    description = description,
    weight = 0,
    isPickupable = false,
    items = items,
), Initializable, Tickable {

    val features = RoomFeatureManager(features.toMutableList())
    val doors: Map<Direction, Door> = doors.associateBy { it.direction }

    // Room messages from NPCs and events
    private val roomMessages = mutableListOf<String>()
    private val maxMessages = 5 // Keep only the last 5 messages

    /**
     * Get the room ID that this room connects to in the given direction.
     */
    fun findExit(direction: Direction): KClass<out Room>? = exits[direction]

    /**
     * Get all available exits from this room.
     */
    fun getAvailableExits(): Set<Direction> = exits.keys

    fun addExit(direction: Direction, exitRoom: KClass<out Room>) {
        exits[direction] = exitRoom
    }

    fun addExit(direction: Direction, exitRoom: Room) {
        addExit(direction, exitRoom::class)
    }

    /**
     * Get a formatted string of available exits.
     */
    fun getExitsDescription(): String {

        val allExits = getAvailableExits()
        if (allExits.isEmpty()) return "There are no obvious exits."

        val exitNames = allExits.map {
            doors[it]?.getExitDescription() ?: it.name.lowercase()
        }

        return "*Exits:* ${exitNames.joinToString(", ")}"
    }

    /**
     * Find a feature in this room.
     */
    fun findFeature(featureName: String) = features.findFeature(featureName)

    /**
     * Check if movement in a direction is blocked by a door.
     * @return A message if movement is blocked, null otherwise
     */
    fun isMovementBlocked(direction: Direction): String? {
        // First check if there's even an exit in that direction.
        if (!exits.containsKey(direction)) return "There is no exit in that direction."

        // Then check if there's a door blocking the exit.
        val door = doors[direction] ?: return null

        return when {
            door.locked -> "The ${door.name} is locked. You need to unlock it first."
            door.closed -> "The ${door.name} is closed. You need to open it first."
            else -> null
        }
    }

    val contentsNonLiving: List<Item> get() = <EMAIL> { it is Living }
    val contentsNPCs: List<NPC> get() = <EMAIL><NPC>()

    /**
     * Get a description of NPCs in the room.
     */
    fun getNPCsDescription(): String {
        val npcs = contentsNPCs
        return if (npcs.isEmpty()) {
            ""
        } else {
            "*Present:* ${npcs.joinToString(", ") { it.name }}"
        }
    }

    /**
     * Get a description of items (excluding NPCs) in the room.
     */
    fun getItemsDescription(): String {
        val nonNpcItems = contentsNonLiving
        return if (nonNpcItems.isEmpty()) {
            ""
        } else {
            "*Items:* ${nonNpcItems.joinToString(", ") { it.getShortDescription() }}"
        }
    }

    /**
     * Get a description that includes items and optionally examinable features.
     */
    fun getFullDescription(showExaminableHint: Boolean = false): String {
        return buildString {
            appendLine("=== _${name}_ ===")
            appendLine()
            appendLine(description)
            appendLine()

            // Show NPCs separately from items
            val npcsDesc = getNPCsDescription()
            if (npcsDesc.isNotEmpty()) {
                appendLine(npcsDesc)
                appendLine()
            }

            // Show items (excluding NPCs)
            val itemsDesc = getItemsDescription()
            if (itemsDesc.isNotEmpty()) {
                appendLine(itemsDesc)
                appendLine()
            }

            if (showExaminableHint && !features.isEmpty()) {
                val examinableThings = features.getExaminableThings()
                if (examinableThings.isNotEmpty()) {
                    appendLine("*You can examine:* ${examinableThings.joinToString(", ")}")
                    appendLine()
                }
            }

            appendLine(getExitsDescription())
        }
    }

    /**
     * Called when a player enters this room.
     * Override to add custom behavior.
     */
    open fun onPlayerEnter(playerId: String) {
        // Default implementation does nothing
    }

    /**
     * Handle NPC reactions when a player enters the room.
     */
    fun handlePlayerEnter(player: Player): String? {
        val npcs = contentsNPCs
        if (npcs.isEmpty()) return null

        val reactions = mutableListOf<String>()
        npcs.forEach { npc ->
            val reaction = npc.onPlayerEnter(player)
            if (reaction != null) {
                reactions.add(reaction)
            }
        }

        return if (reactions.isNotEmpty()) {
            reactions.joinToString("\n")
        } else {
            null
        }
    }

    /**
     * Called when a player leaves this room.
     * Override to add custom behavior.
     */
    open fun onPlayerLeave(playerId: String) {
        // Default implementation does nothing
    }

    override fun onTick(tickCount: Long) {
        // Default implementation does nothing
    }

    override fun onInit() {
        // Default implementation does nothing
    }

    // === ROOM MESSAGES ===

    /**
     * Add a message to the room (from NPCs, events, etc.).
     * These messages will be shown to players when they look or enter.
     */
    fun addRoomMessage(message: String) {
        roomMessages.add(message)
        // Keep only the most recent messages
        if (roomMessages.size > maxMessages) {
            roomMessages.removeAt(0)
        }
    }

    /**
     * Get and clear recent room messages.
     * This is called when a player looks or enters the room.
     */
    fun getAndClearRoomMessages(): List<String> {
        val messages = roomMessages.toList()
        roomMessages.clear()
        return messages
    }

    /**
     * Check if there are any pending room messages.
     */
    fun hasRoomMessages(): Boolean = roomMessages.isNotEmpty()
}
