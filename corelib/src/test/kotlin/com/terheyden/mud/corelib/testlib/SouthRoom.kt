package com.terheyden.mud.corelib.testlib

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.corelib.room.StartingRoom
import org.springframework.stereotype.Component

/**
 * The starting test room in testlib.
 */
@Component
class SouthRoom : Room(
    id = "south_room",
    name = "South Room",
    description = "You stand in the south room. There is a painting on the wall.",
    features = listOf(
        RoomFeature(
            id = "painting",
            names = listOf("painting"),
            description = "The painting is a portrait of a forest scene.",
            keywords = listOf("portrait", "forest", "scene", "forest scene"),
        ),
    ),
    items = listOf(TestKey()),
    exits = mutableMapOf(
        Direction.NORTH to NorthRoom::class,
    ),
    doors = listOf(TestDoor(Direction.NORTH)),
), StartingRoom
