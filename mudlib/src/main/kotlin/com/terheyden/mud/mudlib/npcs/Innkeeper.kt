package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.Ale
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.HealingPotion

/**
 * The friendly innkeeper who provides food, drink, and shelter.
 */
class Innkeeper : NPC(
    id = "innkeeper",
    name = "innkeeper",
    description = "A stout, middle-aged man with a round, friendly face and a welcoming smile. " +
            "His apron is stained with ale and food, evidence of his hard work keeping the " +
            "inn running. His eyes are kind but shrewd - he's clearly seen many travelers " +
            "come and go, and knows how to read people well.",
    inventory = listOf(Ale(), BreadLoaf(), HealingPotion()),
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 2,
    baseAttackPower = 8,
    baseDefense = 6,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasOfferedRoom = false
    private var hasSharedGossip = false

    override fun getGreeting(player: Player): String {
        return "The innkeeper looks up from wiping down a mug and beams at you. 'Welcome to " +
                "The Prancing Pony! Best inn this side of the capital, if I do say so myself. " +
                "What can I get for you - food, drink, or perhaps a room for the night?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("food") || lowerMessage.contains("eat") || lowerMessage.contains("hungry") -> {
                val bread = BreadLoaf()
                player.getCurrentRoom().addItem(bread)
                "The innkeeper's face lights up. 'Ah, you've come to the right place! Here's " +
                        "a fresh loaf of bread, still warm from the oven. My wife bakes the " +
                        "finest bread in the village. That'll fill your belly right up!'"
            }

            lowerMessage.contains("drink") || lowerMessage.contains("ale") || lowerMessage.contains("beer") || lowerMessage.contains(
                "thirsty"
            ) -> {
                val ale = Ale()
                player.getCurrentRoom().addItem(ale)
                "The innkeeper grins and pulls a fresh mug of ale from behind the bar. 'Here " +
                        "you go - our finest ale, brewed right here in the village. It's got " +
                        "a smooth taste and just the right amount of bite. Enjoy!'"
            }

            lowerMessage.contains("room") || lowerMessage.contains("sleep") || lowerMessage.contains("rest") || lowerMessage.contains(
                "stay"
            ) -> {
                if (!hasOfferedRoom) {
                    hasOfferedRoom = true
                    "The innkeeper nods approvingly. 'Aye, we've got comfortable rooms upstairs. " +
                            "Clean beds, warm blankets, and a washbasin. A good night's rest " +
                            "will do you wonders before you continue your adventures. The " +
                            "stairs are right over there.'"
                } else {
                    "The innkeeper gestures toward the stairs. 'The rooms are still available " +
                            "upstairs. Rest well, and don't hesitate to ask if you need anything.'"
                }
            }

            lowerMessage.contains("gossip") || lowerMessage.contains("news") || lowerMessage.contains("rumors") -> {
                if (!hasSharedGossip) {
                    hasSharedGossip = true
                    "The innkeeper leans in and lowers his voice. 'Well, since you asked... " +
                            "There's been strange happenings lately. Lights in the old mine, " +
                            "and some of our folk have gone missing. The village elder is " +
                            "worried sick. Also, that traveling bard has been sharing some " +
                            "interesting tales from the road.'"
                } else {
                    "The innkeeper shrugs. 'That's all the news I have for now. Most of my " +
                            "information comes from travelers like yourself!'"
                }
            }

            lowerMessage.contains("healing") || lowerMessage.contains("potion") || lowerMessage.contains("hurt") || lowerMessage.contains(
                "wounded"
            ) -> {
                if (player.currentHealthPoints < player.maxHealthPoints * 0.8) {
                    val potion = HealingPotion()
                    player.getCurrentRoom().addItem(potion)
                    "The innkeeper's expression turns concerned. 'You look like you've been " +
                            "through some rough times! Here, take this healing potion - on " +
                            "the house. Can't have brave adventurers suffering in my inn!'"
                } else {
                    "The innkeeper looks you over. 'You seem healthy enough to me, but I " +
                            "do keep healing potions on hand for those who need them.'"
                }
            }

            lowerMessage.contains("inn") || lowerMessage.contains("prancing pony") -> {
                "The innkeeper puffs up with pride. 'The Prancing Pony has been in my family " +
                        "for three generations! My grandfather built this place, and we've " +
                        "been serving travelers ever since. We pride ourselves on good food, " +
                        "strong drink, and comfortable beds.'"
            }

            lowerMessage.contains("village") || lowerMessage.contains("people") -> {
                "The innkeeper smiles warmly. 'This is a good village with good people. We " +
                        "look out for each other here. The elder keeps us organized, the " +
                        "temple provides spiritual guidance, and the shop keeps us supplied. " +
                        "It's a peaceful life... mostly.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "The innkeeper waves cheerfully. 'Safe travels, friend! Remember, The Prancing " +
                        "Pony is always here when you need a warm meal and a soft bed. " +
                        "Come back anytime!'"
            }

            else -> {
                val responses = listOf(
                    "The innkeeper nods thoughtfully while polishing a mug.",
                    "The innkeeper's eyes twinkle with the wisdom of someone who's heard many stories.",
                    "The innkeeper leans on the bar, clearly enjoying the conversation.",
                    "The innkeeper chuckles good-naturedly at your words.",
                    "The innkeeper listens with the patience of someone used to chatty travelers."
                )
                responses.random()
            }
        }
    }
}
