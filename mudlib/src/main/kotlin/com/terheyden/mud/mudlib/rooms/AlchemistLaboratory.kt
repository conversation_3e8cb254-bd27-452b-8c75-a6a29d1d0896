package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.puzzle.PuzzleRoom
import com.terheyden.mud.corelib.puzzle.PuzzleState
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.AlchemicalApparatus
import com.terheyden.mud.mudlib.items.ElixirOfWisdom
import com.terheyden.mud.mudlib.puzzles.AlchemyPuzzle
import org.springframework.stereotype.Component

/**
 * An alchemist's laboratory where players must mix ingredients in the correct order
 * to create a powerful elixir.
 */
@Component
class AlchemistLaboratory : PuzzleRoom(
    id = "alchemist_laboratory",
    name = "Alchemist's Laboratory",
    description = "You enter a cluttered alchemist's laboratory filled with bubbling beakers, " +
            "glowing vials, and the scent of exotic herbs. Shelves line the walls, packed with " +
            "jars of mysterious ingredients and ancient alchemical texts. In the center of the " +
            "room stands a large brass cauldron over a magical flame that burns without fuel. " +
            "A workbench nearby holds various measuring tools and a leather-bound recipe book " +
            "open to a page titled 'Elixir of Wisdom'.",
    features = listOf(
        RoomFeature(
            id = "brass_cauldron",
            names = listOf("brass cauldron", "cauldron", "large cauldron", "magical cauldron"),
            description = "A large brass cauldron sits over a magical flame that burns with " +
                    "steady blue fire. The cauldron's surface is polished to a mirror shine " +
                    "and etched with alchemical symbols around its rim. Steam occasionally " +
                    "rises from within, carrying the scent of possibility.",
            keywords = listOf(
                "large",
                "brass",
                "magical",
                "flame",
                "blue",
                "fire",
                "polished",
                "mirror",
                "shine",
                "etched",
                "symbols",
                "rim",
                "steam",
                "possibility"
            )
        ),
        RoomFeature(
            id = "ingredient_shelves",
            names = listOf("ingredient shelves", "shelves", "jars", "ingredients"),
            description = "Tall wooden shelves stretch from floor to ceiling, packed with " +
                    "hundreds of glass jars containing exotic ingredients. Each jar is " +
                    "carefully labeled in flowing script, and many glow with inner light " +
                    "or swirl with mysterious energies.",
            keywords = listOf(
                "tall",
                "wooden",
                "floor",
                "ceiling",
                "hundreds",
                "glass",
                "exotic",
                "labeled",
                "flowing",
                "script",
                "glow",
                "inner",
                "light",
                "swirl",
                "mysterious",
                "energies"
            )
        ),
        RoomFeature(
            id = "recipe_book",
            names = listOf("recipe book", "book", "leather book", "alchemical book"),
            description = "An ancient leather-bound book lies open on the workbench, its " +
                    "pages yellowed with age. The current page shows a recipe for the " +
                    "'Elixir of Wisdom' with detailed instructions and ingredient lists. " +
                    "The text seems to shimmer slightly, as if imbued with magic.",
            keywords = listOf(
                "ancient",
                "leather-bound",
                "yellowed",
                "age",
                "current",
                "page",
                "detailed",
                "instructions",
                "lists",
                "shimmer",
                "imbued",
                "magic"
            )
        ),
        RoomFeature(
            id = "workbench",
            names = listOf("workbench", "bench", "measuring tools", "tools"),
            description = "A sturdy oak workbench holds an array of precise measuring tools: " +
                    "graduated cylinders, delicate scales, silver spoons, and crystal " +
                    "droppers. Everything is arranged with meticulous care, suggesting " +
                    "the importance of precision in alchemical work.",
            keywords = listOf(
                "sturdy",
                "oak",
                "array",
                "precise",
                "graduated",
                "cylinders",
                "delicate",
                "scales",
                "silver",
                "spoons",
                "crystal",
                "droppers",
                "meticulous",
                "care",
                "precision"
            )
        ),
        RoomFeature(
            id = "moonflower_jar",
            names = listOf("moonflower jar", "moonflower", "silvery petals", "glowing petals"),
            description = "A glass jar containing silvery moonflower petals that glow softly " +
                    "in the dim light. The label reads 'Moonflower Petals - For Clarity of Mind'.",
            keywords = listOf(
                "glass",
                "jar",
                "silvery",
                "petals",
                "glow",
                "softly",
                "dim",
                "light",
                "label",
                "clarity",
                "mind"
            )
        ),
        RoomFeature(
            id = "dragon_scale_jar",
            names = listOf("dragon scale jar", "dragon scale", "red scale", "shimmering scale"),
            description = "A reinforced jar holds a single shimmering red dragon scale that " +
                    "radiates warmth. The label reads 'Dragon Scale - For Power and Strength'.",
            keywords = listOf("reinforced", "single", "shimmering", "red", "radiates", "warmth", "power", "strength")
        ),
        RoomFeature(
            id = "spring_water_jar",
            names = listOf("spring water jar", "spring water", "crystal water", "sacred water"),
            description = "A crystal-clear jar contains pure spring water that seems to " +
                    "sparkle with inner light. The label reads 'Sacred Spring Water - The Pure Base'.",
            keywords = listOf("crystal-clear", "pure", "sparkle", "inner", "light", "sacred", "pure", "base")
        ),
        RoomFeature(
            id = "phoenix_feather_jar",
            names = listOf("phoenix feather jar", "phoenix feather", "golden feather", "burning feather"),
            description = "A special heat-resistant jar contains a golden phoenix feather that " +
                    "never burns. The label reads 'Phoenix Feather - For Transformation'.",
            keywords = listOf("special", "heat-resistant", "golden", "never", "burns", "transformation")
        ),
        RoomFeature(
            id = "nightshade_jar",
            names = listOf("nightshade jar", "nightshade", "purple liquid", "dark extract"),
            description = "A dark jar contains purple nightshade extract that swirls with " +
                    "mysterious properties. The label reads 'Nightshade Extract - For Mystery and Depth'.",
            keywords = listOf("dark", "purple", "swirls", "mysterious", "properties", "mystery", "depth")
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to TowerBasement::class,
        Direction.WEST to ClockworkChamber::class,
        Direction.NORTH to MirrorMaze::class,
    ),
    items = listOf(
        // Items will be revealed when puzzle is solved
    ),
) {

    private val alchemyPuzzle = AlchemyPuzzle()

    override fun handlePuzzleInteraction(playerId: String, action: String, target: String): String? {
        return when (action.lowercase()) {
            "add", "mix", "put", "use" -> handleIngredientAddition(target)
            "examine", "look" -> handleExamination(target)
            "read" -> handleReading(target)
            else -> null
        }
    }

    private fun handleIngredientAddition(target: String): String {
        if (puzzleState == PuzzleState.SOLVED) {
            return "The Elixir of Wisdom has already been successfully created. The cauldron glows with golden light."
        }

        if (alchemyPuzzle.hasMixtureFailed()) {
            return "The mixture in the cauldron has been ruined. You need to clean it out and start over. Try 'use cauldron' to reset."
        }

        val (ingredient, quantity) = parseIngredientCommand(target)
        if (ingredient == null) {
            return "You must specify an ingredient to add. Available ingredients: moonflower, dragon scale, spring water, phoenix feather, nightshade."
        }

        val result = alchemyPuzzle.addIngredient(ingredient, quantity)

        return when (result) {
            AlchemyPuzzle.AlchemyResult.STEP_CORRECT -> {
                puzzleState = PuzzleState.IN_PROGRESS
                "You carefully add the ingredient to the cauldron. The mixture bubbles pleasantly, " +
                        "and the color shifts in a promising way. Step ${alchemyPuzzle.getCurrentStep()} of " +
                        "${alchemyPuzzle.getTotalSteps()} complete.\n\n${alchemyPuzzle.getCauldronDescription()}"
            }

            AlchemyPuzzle.AlchemyResult.RECIPE_COMPLETE -> {
                onPuzzleSolved("player")
                revealTreasures()
                "You add the final ingredient and the mixture transforms into a perfect golden elixir! " +
                        "The Elixir of Wisdom glows with inner light, and you feel wiser just looking at it. " +
                        "A hidden compartment in the workbench opens, revealing additional alchemical treasures!"
            }

            AlchemyPuzzle.AlchemyResult.WRONG_INGREDIENT -> {
                "The mixture reacts violently to the wrong ingredient! Dark smoke billows from the " +
                        "cauldron and the mixture is ruined. You'll need to clean it out and start over."
            }

            AlchemyPuzzle.AlchemyResult.WRONG_QUANTITY -> {
                "The wrong amount of ingredient causes the mixture to bubble angrily and turn an " +
                        "unpleasant color. The mixture is ruined - you'll need to start over."
            }

            AlchemyPuzzle.AlchemyResult.TOO_MANY_INGREDIENTS -> {
                "You've added too many ingredients! The mixture overflows and is ruined."
            }

            AlchemyPuzzle.AlchemyResult.INVALID_INGREDIENT -> {
                "That ingredient is not available. Check the shelves for: moonflower, dragon scale, " +
                        "spring water, phoenix feather, nightshade."
            }

            AlchemyPuzzle.AlchemyResult.INVALID_QUANTITY -> {
                "You must specify a valid quantity (a positive number)."
            }

            AlchemyPuzzle.AlchemyResult.INSUFFICIENT_INGREDIENT -> {
                "There isn't enough of that ingredient available."
            }

            AlchemyPuzzle.AlchemyResult.MIXTURE_RUINED -> {
                "The mixture is already ruined. Clean the cauldron first."
            }

            AlchemyPuzzle.AlchemyResult.ALREADY_COMPLETE -> {
                "The elixir is already complete!"
            }
        }
    }

    private fun handleExamination(target: String): String? {
        when {
            target.contains("cauldron") -> {
                return alchemyPuzzle.getCauldronDescription()
            }

            target.contains("recipe") || target.contains("book") -> {
                return getRecipeText()
            }

            else -> {
                val ingredient = extractIngredientName(target)
                if (ingredient != null) {
                    return alchemyPuzzle.getIngredientDescription(ingredient)
                }
            }
        }
        return null
    }

    private fun handleReading(target: String): String? {
        if (target.contains("recipe") || target.contains("book")) {
            return getRecipeText()
        }
        return null
    }

    private fun parseIngredientCommand(command: String): Pair<String?, Int> {
        val parts = command.lowercase().split(" ")
        var quantity = 1
        var ingredientName: String? = null

        // Look for quantity
        for (part in parts) {
            part.toIntOrNull()?.let { quantity = it }
        }

        // Extract ingredient name
        ingredientName = extractIngredientName(command)

        return Pair(ingredientName, quantity)
    }

    private fun extractIngredientName(text: String): String? {
        val normalized = text.lowercase()
        return when {
            normalized.contains("moonflower") -> "moonflower"
            normalized.contains("dragon") && normalized.contains("scale") -> "dragon_scale"
            normalized.contains("spring") && normalized.contains("water") -> "spring_water"
            normalized.contains("phoenix") && normalized.contains("feather") -> "phoenix_feather"
            normalized.contains("nightshade") -> "nightshade"
            else -> null
        }
    }

    private fun getRecipeText(): String {
        val hint = alchemyPuzzle.getCurrentHint()
        return if (hint != null) {
            "The recipe book shows the steps for creating the Elixir of Wisdom. " +
                    "Current step: $hint\n\n" +
                    "Progress: ${alchemyPuzzle.getCurrentStep()} of ${alchemyPuzzle.getTotalSteps()} steps completed."
        } else if (alchemyPuzzle.isRecipeComplete()) {
            "The recipe has been completed successfully! The Elixir of Wisdom is ready."
        } else {
            "The recipe book contains the formula for the Elixir of Wisdom, but the mixture has failed. " +
                    "Clean the cauldron and start over."
        }
    }

    private fun revealTreasures() {
        addItem(ElixirOfWisdom())
        addItem(AlchemicalApparatus())
    }

    override fun getSolvedDescription(): String {
        return "The laboratory now glows with the success of perfect alchemy. The golden Elixir of " +
                "Wisdom sits in the cauldron, and a hidden compartment has revealed additional treasures."
    }

    override fun resetPuzzle() {
        super.resetPuzzle()
        alchemyPuzzle.reset()
        removeItem("elixir_of_wisdom")
        removeItem("alchemical_apparatus")
    }

    override fun onPlayerEnter(playerId: String) {
        if (puzzleState == PuzzleState.UNSOLVED) {
            // Could provide initial guidance about reading the recipe book
        }
    }
}
