package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.SkeletonKey
import com.terheyden.mud.mudlib.npcs.SkeletonWarrior
import com.terheyden.mud.mudlib.weapons.BattleAxe
import org.springframework.stereotype.Component

/**
 * A dangerous dungeon chamber guarded by undead creatures.
 */
@Component
class DungeonChamber : Room(
    id = "dungeon_chamber",
    name = "Dungeon Chamber",
    description = "You enter a large, ominous chamber deep underground. The walls are built " +
            "from massive stone blocks, and iron chains hang from the ceiling. Ancient weapon " +
            "racks line the walls, most empty but some still holding rusted arms. In the center " +
            "of the room stands a stone altar, stained dark with age. The air is cold and still, " +
            "carrying the unmistakable feeling of death and decay.",
    features = listOf(
        RoomFeature(
            id = "iron_chains",
            names = listOf("iron chains", "chains", "hanging chains"),
            description = "Heavy iron chains hang from the vaulted ceiling, some ending in manacles " +
                    "and others in hooks. They sway slightly despite the still air, creating " +
                    "an eerie creaking sound that echoes through the chamber. The metal is " +
                    "pitted with rust and age.",
            keywords = listOf("heavy", "vaulted", "manacles", "hooks", "sway", "creaking", "pitted", "rust")
        ),
        RoomFeature(
            id = "weapon_racks",
            names = listOf("weapon racks", "racks", "ancient racks", "empty racks"),
            description = "Wooden weapon racks stand against the walls, their dark wood warped " +
                    "with age and moisture. Most are empty, but a few still hold the remnants " +
                    "of ancient weapons - rusted swords, broken spears, and cracked shields. " +
                    "These were once the arms of warriors long dead.",
            keywords = listOf("wooden", "warped", "moisture", "remnants", "rusted", "broken", "cracked", "warriors")
        ),
        RoomFeature(
            id = "stone_altar",
            names = listOf("stone altar", "altar", "dark altar", "stained altar"),
            description = "The altar is carved from a single block of black stone, its surface " +
                    "stained with dark patches that could be blood or something worse. Strange " +
                    "runes are carved around its edges, and you feel an aura of malevolent " +
                    "power emanating from it. This was clearly used for dark rituals.",
            keywords = listOf("carved", "black", "blood", "worse", "runes", "edges", "malevolent", "rituals")
        ),
    ),
    exits = mutableMapOf(
        Direction.SOUTH to UndergroundTunnel::class,
        Direction.EAST to AbandonedMine::class,
    ),
    items = listOf(
        SkeletonWarrior(),
        BattleAxe(),
        SkeletonKey(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The chamber could have dangerous effects
        // - Undead encounters
        // - Cursed altar interactions
        // - Trap mechanisms
    }
}
