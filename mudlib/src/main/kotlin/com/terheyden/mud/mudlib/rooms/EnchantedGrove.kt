package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.EnchantedFlower
import com.terheyden.mud.mudlib.items.MagicalBerries
import com.terheyden.mud.mudlib.npcs.ForestSpirit
import org.springframework.stereotype.Component

/**
 * A mystical grove where nature magic runs strong and ancient spirits dwell.
 */
@Component
class EnchantedGrove : Room(
    id = "enchanted_grove",
    name = "Enchanted Grove",
    description = "You step into a _magical grove_ where the very air *shimmers with enchantment*. " +
            "Ancient trees with `silver bark` reach toward the sky, their leaves glowing with " +
            "soft, _ethereal light_. Flowers of *impossible colors* bloom year-round, and the " +
            "grass beneath your feet feels alive with `magical energy`. Butterflies with " +
            "_translucent wings_ flutter between the trees, leaving trails of *sparkling dust*.",
    features = listOf(
        RoomFeature(
            id = "silver_trees",
            names = listOf("silver trees", "trees", "ancient trees", "silver bark"),
            description = "These magnificent trees are unlike any you've seen before. Their bark " +
                    "gleams like polished silver, and their leaves emit a gentle, pulsing light " +
                    "that changes color with the seasons. The trees seem ancient beyond measure, " +
                    "and you can feel their wisdom in the air around them.",
            keywords = listOf("magnificent", "gleams", "polished", "emit", "pulsing", "seasons", "ancient", "wisdom")
        ),
        RoomFeature(
            id = "magical_flowers",
            names = listOf("magical flowers", "flowers", "impossible flowers", "glowing flowers"),
            description = "Flowers of every imaginable color - and some that shouldn't exist - " +
                    "bloom throughout the grove. Some petals shift through rainbow hues, others " +
                    "glow like tiny stars, and a few seem to sing in harmonious whispers when " +
                    "the wind touches them. Their fragrance is intoxicating and magical.",
            keywords = listOf(
                "imaginable",
                "exist",
                "shift",
                "rainbow",
                "hues",
                "stars",
                "sing",
                "harmonious",
                "whispers",
                "intoxicating"
            )
        ),
        RoomFeature(
            id = "living_grass",
            names = listOf("living grass", "grass", "magical grass", "glowing grass"),
            description = "The grass beneath your feet is more than just vegetation - it pulses " +
                    "with life and magic. Each blade seems to respond to your presence, bending " +
                    "toward you as you move. The grass glows faintly with green light, creating " +
                    "patterns that shift and dance across the grove floor.",
            keywords = listOf(
                "vegetation",
                "pulses",
                "blade",
                "respond",
                "presence",
                "bending",
                "faintly",
                "patterns",
                "dance"
            )
        ),
        RoomFeature(
            id = "magical_butterflies",
            names = listOf("butterflies", "magical butterflies", "translucent butterflies", "sparkling butterflies"),
            description = "Ethereal butterflies with wings like stained glass flutter throughout " +
                    "the grove. Their wings are translucent and seem to be made of pure light, " +
                    "leaving trails of sparkling magical dust wherever they fly. They appear " +
                    "to be drawn to sources of magic and positive emotion.",
            keywords = listOf(
                "ethereal",
                "stained",
                "glass",
                "translucent",
                "pure",
                "trails",
                "sparkling",
                "drawn",
                "positive",
                "emotion"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to SecretGarden::class,
    ),
    items = listOf(
        ForestSpirit(),
        MagicalBerries(),
        EnchantedFlower(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The grove could have powerful magical effects
        // - Healing and restoration
        // - Magical ability enhancement
        // - Communication with nature spirits
        // - Temporary magical transformations
    }
}
