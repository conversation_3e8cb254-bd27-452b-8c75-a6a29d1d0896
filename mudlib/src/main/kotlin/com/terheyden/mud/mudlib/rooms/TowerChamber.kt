package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.AncientScroll
import com.terheyden.mud.mudlib.items.CrystalOrb
import com.terheyden.mud.mudlib.weapons.IronSword
import org.springframework.stereotype.Component

/**
 * The mystical chamber at the top of the tower.
 */
@Component
class TowerChamber : Room(
    id = "tower_chamber",
    name = "Tower Chamber",
    description = "You are in a circular chamber at the top of the tower. " +
            "Ancient books and scrolls line the walls, and a crystal orb glows softly on a pedestal " +
            "in the center of the room. Through the narrow windows, you can see the forest stretching " +
            "out in all directions.",
    features = listOf(
        RoomFeature(
            id = "ancient_books",
            names = listOf("ancient books", "books", "tomes"),
            description = "The shelves are lined with ancient tomes bound in leather and strange materials. " +
                    "Many of the titles are written in languages you don't recognize. Some books seem " +
                    "to glow faintly, while others appear to shift and move when you're not looking " +
                    "directly at them.",
            keywords = listOf("shelves", "tomes", "leather", "titles", "languages", "glow", "glowing")
        ),
        RoomFeature(
            id = "scrolls",
            names = listOf("scrolls", "parchments"),
            description = "Numerous scrolls are scattered about the chamber, some rolled up neatly on shelves, " +
                    "others spread open on reading stands. The parchment appears to be made from unusual " +
                    "materials, and the ink seems to shimmer with its own inner light.",
            keywords = listOf("parchment", "ink", "reading", "stands", "scattered", "shimmer")
        ),
        RoomFeature(
            id = "pedestal",
            names = listOf("pedestal", "stone pedestal"),
            description = "The pedestal in the center of the room is carved from a single piece of white marble. " +
                    "Intricate patterns spiral up its surface, and it radiates a subtle warmth. " +
                    "The crystal orb rests perfectly in a depression at its top, " +
                    "as if the pedestal was made specifically for it.",
            keywords = listOf("marble", "patterns", "warmth", "depression", "carved", "white")
        ),
        RoomFeature(
            id = "narrow_windows",
            names = listOf("narrow windows", "windows"),
            description = "The narrow windows are cut deep into the thick stone walls. Through them, you can " +
                    "see the forest stretching out in all directions like a green sea. " +
                    "The view is breathtaking, and you can even make out the crystal cave entrance to the east.",
            keywords = listOf("view", "forest", "green", "sea", "breathtaking", "east", "cave")
        ),
    ),
    exits = mutableMapOf(
        Direction.DOWN to TowerEntrance::class,
        Direction.NORTH to AncientLibrary::class,
    ),
    items = listOf(
        AncientScroll(),
        CrystalOrb(),
        IronSword(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The crystal orb could react to certain players or conditions
        // Could trigger magical events or provide visions
    }
}
