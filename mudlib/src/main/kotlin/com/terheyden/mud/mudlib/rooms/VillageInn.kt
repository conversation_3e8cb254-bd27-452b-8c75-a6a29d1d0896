package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.Ale
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.npcs.Innkeeper
import org.springframework.stereotype.Component

/**
 * A cozy village inn where travelers can rest and share stories.
 */
@Component
class VillageInn : Room(
    id = "village_inn",
    name = "The Prancing Pony Inn",
    description = "You enter a warm and welcoming inn filled with the sounds of laughter and " +
            "conversation. A large stone fireplace crackles merrily in one corner, casting " +
            "dancing shadows on the wooden walls. Heavy oak tables and chairs are scattered " +
            "throughout the room, occupied by locals and travelers sharing meals and stories. " +
            "The air is thick with the aroma of roasted meat, fresh bread, and ale.",
    features = listOf(
        RoomFeature(
            id = "stone_fireplace",
            names = listOf("stone fireplace", "fireplace", "crackling fire", "fire"),
            description = "The large stone fireplace dominates one wall, its hearth filled with " +
                    "a cheerful fire that provides both warmth and light. Above the mantle " +
                    "hangs a painting of a prancing pony, the inn's namesake. Iron tools " +
                    "for tending the fire rest nearby.",
            keywords = listOf(
                "dominates",
                "hearth",
                "cheerful",
                "warmth",
                "mantle",
                "painting",
                "pony",
                "namesake",
                "iron",
                "tools"
            )
        ),
        RoomFeature(
            id = "oak_tables",
            names = listOf("oak tables", "tables", "heavy tables", "wooden tables"),
            description = "Sturdy oak tables fill the common room, their surfaces polished smooth " +
                    "by years of use. Each table bears the marks of countless meals and " +
                    "conversations - ring stains from mugs, small scratches, and the occasional " +
                    "carved initial. They're perfectly sized for intimate gatherings.",
            keywords = listOf(
                "sturdy",
                "common",
                "polished",
                "marks",
                "countless",
                "ring",
                "stains",
                "scratches",
                "intimate"
            )
        ),
        RoomFeature(
            id = "inn_bar",
            names = listOf("bar", "inn bar", "wooden bar", "counter"),
            description = "A long wooden bar runs along one side of the room, its surface worn " +
                    "smooth by countless elbows and mugs. Behind it, shelves hold an impressive " +
                    "collection of bottles, kegs, and drinking vessels. The bar is clearly the " +
                    "heart of the inn's social activity.",
            keywords = listOf(
                "long",
                "worn",
                "elbows",
                "mugs",
                "shelves",
                "impressive",
                "bottles",
                "kegs",
                "vessels",
                "social"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to VillageSquare::class,
        Direction.EAST to VillageBakery::class,
    ),
    items = listOf(
        Innkeeper(),
        Ale(),
        BreadLoaf(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The inn could offer special services
        // - Rest and healing
        // - Information gathering
        // - Room rental
        // - Special events and entertainment
    }
}
