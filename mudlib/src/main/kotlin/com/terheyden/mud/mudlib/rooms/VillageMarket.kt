package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.SilverRing
import com.terheyden.mud.mudlib.npcs.GeneralMerchant
import com.terheyden.mud.mudlib.npcs.JewelryMerchant
import com.terheyden.mud.mudlib.npcs.PotionSeller
import org.springframework.stereotype.Component

/**
 * A bustling marketplace where villagers and travelers come to trade goods.
 */
@Component
class VillageMarket : Room(
    id = "village_market",
    name = "Village Market",
    description = "You enter a lively marketplace filled with the sounds of haggling merchants and " +
            "chattering customers. Colorful stalls and wooden booths line the cobblestone square, " +
            "each displaying an array of goods - from fresh produce and handcrafted items to " +
            "mysterious potions and gleaming jewelry. The air is filled with the mingled scents " +
            "of spices, leather, and fresh flowers. Banners and awnings provide shade from the sun.",
    features = listOf(
        RoomFeature(
            id = "merchant_stalls",
            names = listOf("merchant stalls", "stalls", "booths", "market stalls"),
            description = "Dozens of wooden stalls and booths are arranged throughout the market square. " +
                    "Each stall is unique - some have colorful awnings, others display their wares " +
                    "on wooden tables or in wicker baskets. The merchants call out their prices " +
                    "and advertise their goods with enthusiasm.",
            keywords = listOf(
                "dozens",
                "wooden",
                "arranged",
                "unique",
                "colorful",
                "awnings",
                "tables",
                "wicker",
                "baskets",
                "call",
                "prices",
                "advertise",
                "enthusiasm"
            )
        ),
        RoomFeature(
            id = "produce_displays",
            names = listOf("produce displays", "produce", "fresh produce", "fruits", "vegetables"),
            description = "Fresh fruits and vegetables are artfully arranged in wicker baskets and " +
                    "wooden crates. Bright red apples, golden pears, leafy greens, and root " +
                    "vegetables create a colorful display. The produce looks fresh and appetizing, " +
                    "clearly brought in from local farms this morning.",
            keywords = listOf(
                "artfully",
                "arranged",
                "wicker",
                "crates",
                "bright",
                "red",
                "apples",
                "golden",
                "pears",
                "leafy",
                "greens",
                "root",
                "colorful",
                "appetizing",
                "local",
                "farms",
                "morning"
            )
        ),
        RoomFeature(
            id = "spice_vendors",
            names = listOf("spice vendors", "spices", "spice stalls", "aromatic spices"),
            description = "Several stalls specialize in exotic spices and herbs. Small cloth bags " +
                    "and wooden bowls contain colorful powders and dried leaves that fill the " +
                    "air with complex, enticing aromas. The spice merchants are eager to explain " +
                    "the uses and origins of their rare ingredients.",
            keywords = listOf(
                "exotic",
                "herbs",
                "cloth",
                "bags",
                "bowls",
                "colorful",
                "powders",
                "dried",
                "leaves",
                "complex",
                "enticing",
                "aromas",
                "eager",
                "explain",
                "origins",
                "rare",
                "ingredients"
            )
        ),
        RoomFeature(
            id = "craft_booths",
            names = listOf("craft booths", "crafts", "handcrafted items", "artisan goods"),
            description = "Local artisans display their handcrafted wares - leather goods, pottery, " +
                    "woven textiles, and carved wooden items. Each piece shows the skill and " +
                    "dedication of its maker. The craftsmanship is impressive, ranging from " +
                    "practical everyday items to decorative works of art.",
            keywords = listOf(
                "local",
                "artisans",
                "handcrafted",
                "wares",
                "leather",
                "pottery",
                "woven",
                "textiles",
                "carved",
                "wooden",
                "skill",
                "dedication",
                "maker",
                "craftsmanship",
                "impressive",
                "practical",
                "everyday",
                "decorative",
                "art"
            )
        ),
        RoomFeature(
            id = "market_fountain",
            names = listOf("market fountain", "fountain", "small fountain", "trading fountain"),
            description = "A smaller fountain than the one in the village square sits at the center " +
                    "of the market. It's decorated with carvings of merchants and traders, and " +
                    "the sound of flowing water provides a pleasant backdrop to the market noise. " +
                    "Many deals are sealed with handshakes beside this fountain.",
            keywords = listOf(
                "smaller",
                "center",
                "decorated",
                "carvings",
                "merchants",
                "traders",
                "flowing",
                "water",
                "pleasant",
                "backdrop",
                "noise",
                "deals",
                "sealed",
                "handshakes"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to VillageSquare::class,
        Direction.NORTH to VillageBlacksmith::class,
        Direction.SOUTH to VillageBakery::class,
        Direction.EAST to VillageDocks::class,
    ),
    items = listOf(
        GeneralMerchant(),
        PotionSeller(),
        JewelryMerchant(),
        GoldCoins(),
        HealingPotion(),
        SilverRing(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add random market events, special sales, or merchant interactions
    }
}
