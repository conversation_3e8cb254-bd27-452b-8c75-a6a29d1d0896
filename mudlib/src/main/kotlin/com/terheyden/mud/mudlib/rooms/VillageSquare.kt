package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.VillageNotice
import com.terheyden.mud.mudlib.npcs.TravelingBard
import com.terheyden.mud.mudlib.npcs.VillageElder
import org.springframework.stereotype.Component

/**
 * The heart of a small village, bustling with activity and friendly faces.
 */
@Component
class VillageSquare : Room(
    id = "village_square",
    name = "Village Square",
    description = "You stand in the center of a _charming village square_, paved with smooth " +
            "cobblestones worn by generations of feet. A beautiful *stone fountain* bubbles " +
            "merrily in the center, surrounded by wooden benches where villagers gather to " +
            "chat and rest. `Colorful flower boxes` adorn the windows of the surrounding " +
            "buildings, and the smell of _fresh bread_ wafts from a nearby bakery.",
    features = listOf(
        RoomFeature(
            id = "stone_fountain",
            names = listOf("stone fountain", "fountain", "bubbling fountain"),
            description = "The fountain is carved from white marble and depicts a graceful maiden " +
                    "pouring water from an urn. Crystal-clear water bubbles up from the center " +
                    "and cascades down in gentle streams. Copper coins glint at the bottom, " +
                    "thrown by villagers making wishes.",
            keywords = listOf("marble", "maiden", "urn", "crystal", "cascades", "streams", "copper", "coins", "wishes")
        ),
        RoomFeature(
            id = "wooden_benches",
            names = listOf("wooden benches", "benches", "village benches"),
            description = "Several sturdy oak benches are arranged around the fountain, their wood " +
                    "polished smooth by countless hours of use. Each bench bears small carvings " +
                    "- initials, hearts, and simple drawings left by villagers over the years. " +
                    "They provide a perfect spot to rest and watch village life.",
            keywords = listOf("sturdy", "oak", "polished", "countless", "carvings", "initials", "hearts", "drawings")
        ),
        RoomFeature(
            id = "flower_boxes",
            names = listOf("flower boxes", "flowers", "colorful flowers", "window boxes"),
            description = "Bright flower boxes hang from every window, overflowing with colorful " +
                    "blooms - red geraniums, purple petunias, and yellow marigolds. The flowers " +
                    "are clearly well-tended, and their sweet fragrance fills the air. Bees " +
                    "buzz lazily from bloom to bloom.",
            keywords = listOf(
                "bright",
                "overflowing",
                "geraniums",
                "petunias",
                "marigolds",
                "well-tended",
                "fragrance",
                "bees"
            )
        ),
        RoomFeature(
            id = "village_notice_board",
            names = listOf("notice board", "board", "village board", "bulletin board"),
            description = "A wooden notice board stands near the fountain, covered with various " +
                    "announcements, job postings, and village news. The notices are held in " +
                    "place with small iron nails, and the wood is weathered but well-maintained.",
            keywords = listOf("wooden", "announcements", "postings", "news", "nails", "weathered", "well-maintained")
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to ForestClearing::class,
        Direction.SOUTH to VillageInn::class,
        Direction.EAST to VillageMarket::class,
        Direction.WEST to VillageTemple::class,
    ),
    items = listOf(
        VillageElder(),
        TravelingBard(),
        VillageNotice(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The village square could have special events
        // - Market days with special vendors
        // - Festivals and celebrations
        // - Village meetings and announcements
    }
}
